<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>
  
</head>

<body>
  <!--  Body Wrapper -->
  <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
    data-sidebar-position="fixed" data-header-position="fixed">

    <!--  App Topstrip -->
    
    <!-- Sidebar Start -->
<app-navebar></app-navebar>

    <!--  Sidebar End -->
    <!--  Main wrapper -->
    <div class="body-wrapper">
      <!--  Header Start -->
      <header class="app-header">

      </header>
      <!--  Header End -->
      <div class="body-wrapper-inner">
        <div class="container-fluid">
                <div class="welcome-header">
  <h1>Bienvenue dans votre espace</h1>
  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>

</div>
<!-- Bouton pour ouvrir la modale -->

<div class="header-container">
  <div class="header-text">
    <h2>Équipements</h2>
    <p>Gérez les différents types d'équipements informatiques

</p>
  </div>
<button class="add-user-btn" (click)="openModal()" >
  <span class="icon">+</span>Nouvel équipement

</button>
</div>
<div class="search-wrapper">
  <div class="custom-search">
    <input
      type="text"
      placeholder="Rechercher un equipement..."
      [(ngModel)]="searchTerm"
      (input)="onSearchChange()"
      class="form-control"
    />
    <span class="icon-search"></span>
  </div>
</div>

<!-- Formulaire de recherche avancée -->
<div class="advanced-search-wrapper">
  <div class="advanced-search-container">
    <div class="search-header">
      <h4 class="search-title">
        <span class="search-icon">🔍</span>
        Recherche Avancée
      </h4>
      <p class="search-subtitle">Filtrez les équipements par utilisateur et statut</p>
    </div>

    <div class="search-form">
      <div class="search-row">
        <!-- Recherche par utilisateur -->
        <div class="search-field">
          <label class="search-label">
            <span class="label-icon">👤</span>
            Utilisateur
          </label>
          <div class="input-with-icon">
            <input
              type="text"
              class="search-input"
              placeholder="Nom de l'utilisateur..."
            />
            <span class="input-icon">👤</span>
          </div>
        </div>

        <!-- Recherche par statut -->
        <div class="search-field">
          <label class="search-label">
            <span class="label-icon">📊</span>
            Statut
          </label>
          <div class="select-with-icon">
            <select class="search-select">
              <option value="">Tous les statuts</option>
              <option value="DISPONIBLE">Disponible</option>
              <option value="AFFECTE">Affecté</option>
              <option value="MAINTENANCE">En maintenance</option>
              <option value="HORS_SERVICE">Hors service</option>
            </select>
            <span class="select-icon">📊</span>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="search-actions">
          <button class="btn-search">
            <span class="btn-icon">🔍</span>
            Rechercher
          </button>
          <button class="btn-reset">
            <span class="btn-icon">🔄</span>
            Réinitialiser
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

</div>
<!-- Modal -->

<!-- Modal de modification -->
<div class="modal" [ngClass]="{'show': isEditModalOpen}" (click)="closeOnOutsideClickEdit($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeEditModal()">&times;</span>
    <h3 style="font-size: 20px; margin-bottom: -10px;">Modifier l'équipement</h3>
    <form [formGroup]="editForm" (ngSubmit)="onEditSubmit()" novalidate>
      <br><br>

      <!-- 🧩 Model -->
      <mat-form-field appearance="outline" style="width: 100%;">
        <mat-label>Modèle</mat-label>
        <input type="text"
               matInput
               formControlName="model"
               [matAutocomplete]="autoModelEdit"
               placeholder="Rechercher un modèle...">
        <mat-autocomplete #autoModelEdit="matAutocomplete"
                          [displayWith]="displayModel"
                          (optionSelected)="onModelSelectedForEdit($event.option.value)">
          <mat-option *ngFor="let model of modelet" [value]="model">
            {{ model.nomModel }}
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>

      <div *ngIf="editForm.get('model')?.invalid && (editForm.get('model')?.touched || submitted)" style="color:red">
        Le modèle est requis
      </div>

      <!-- 🧩 Numéro de série -->
      <label for="numSerieEdit" style="font-size: 14px; font-weight: 500; color: #000000;">Numéro de série</label>
      <input
        class="form-inputp"
        id="numSerieEdit"
        type="text"
        formControlName="numSerie"
        placeholder="Entrer le numéro de série"
      />
      <div *ngIf="editForm.get('numSerie')?.invalid && (editForm.get('numSerie')?.touched || submitted)" style="color:red">
        Le numéro de série est requis (min 4 caractères)
      </div>

      <!-- 🧩 Description -->
      <label for="descriptionEdit" style="font-size: 14px; font-weight: 500; color: #000000;">Description (optionnel)</label>
      <input
        class="form-inputp"
        id="descriptionEdit"
        type="text"
        formControlName="description"
        placeholder="Entrer la description (optionnel)"
      />

      <!-- 🧩 Date -->
      <label for="dateAffectationEdit" style="font-size: 14px; font-weight: 500; color: #000000;">Date d'acquisition</label>
      <input
        type="date"
        id="dateAffectationEdit"
        class="form-control"
        formControlName="dateAffectation"
      />
      <div *ngIf="editForm.get('dateAffectation')?.invalid && (editForm.get('dateAffectation')?.touched || submitted)" style="color:red">
        La date est requise
      </div>

      <label for="fournisseurEdit" style="font-size: 14px; font-weight: 500; color: #000000;">Fournisseur</label>
      <select
        class="form-inputp"
        id="fournisseurEdit"
        name="fournisseurEdit"
        formControlName="fournisseurs"
        style="width: 100%;"
        required
    >
        <option [ngValue]="null" disabled hidden>Sélectionner fournisseur</option>
        <option *ngFor="let fournisseur of fournisseurs" [ngValue]="fournisseur">
          {{ fournisseur.nomFournisseur }}
        </option>
      </select>
      <div *ngIf="editForm.get('fournisseurs')?.invalid && (editForm.get('fournisseurs')?.touched || submitted)" style="color:red">
        Au moins un fournisseur est requis
      </div>

      <!-- 🧩 Image -->
      <label for="imageEdit" style="font-size: 14px; font-weight: 500; color: #000000;">Logo d'équipement (optionnel)</label>
      <input
        type="file"
        id="imageEdit"
        (change)="onFileSelected($event)"
        accept="image/*"
        class="form-inputp"
      />
      <div *ngIf="imagePreview" style="margin-top: 10px;">
        <img [src]="imagePreview" alt="Aperçu" style="width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;" />
      </div>

      <br />
      <button type="submit" class="btn btn-primary">
        Modifier
      </button>
    </form>
  </div>
</div>




<div class="modal" [ngClass]="{'show': isModalOpen}" (click)="closeOnOutsideClick($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeModal()">&times;</span>
    <h3 style="font-size: 20px; margin-bottom: -10px;" >Ajouter un nouvel Equipement</h3>
<form [formGroup]="form" (ngSubmit)="onRegister()" novalidate>
  <br><br>

  <!-- 🧩 Model -->
<mat-form-field appearance="outline" style="width: 100%;">
  <mat-label>Modèle</mat-label>
  <input type="text"
         matInput
         formControlName="model"
         [matAutocomplete]="autoModel"
         placeholder="Rechercher un modèle...">
  <mat-autocomplete #autoModel="matAutocomplete"
                    [displayWith]="displayModel"
                    (optionSelected)="onModelSelectedForAdd($event.option.value)">
    <mat-option *ngFor="let model of modelet" [value]="model">
      {{ model.nomModel }}
    </mat-option>
  </mat-autocomplete>
</mat-form-field>

  <div *ngIf="form.get('model')?.invalid && (form.get('model')?.touched || submitted)" style="color:red">
    Le modèle est requis
  </div>

  <!-- 🧩 Numéro de série -->
  <label for="numSerie" style="font-size: 14px; font-weight: 500; color: #000000;">Numéro de série</label>
  <input
    class="form-inputp"
    id="numSerie"
    type="text"
    formControlName="numSerie"
    placeholder="Entrer le numéro de série"
  />
  <div *ngIf="form.get('numSerie')?.invalid && (form.get('numSerie')?.touched || submitted)" style="color:red">
    Le numéro de série est requis (min 4 caractères)
  </div>

  <!-- 🧩 Description -->
  <label for="description" style="font-size: 14px; font-weight: 500; color: #000000;">Description (optionnel)</label>
  <input
    class="form-inputp"
    id="description"
    type="text"
    formControlName="description"
    placeholder="Entrer la description (optionnel)"
  />

  <!-- 🧩 Date -->
  <label for="dateAffectation" style="font-size: 14px; font-weight: 500; color: #000000;">Date d'acquisition</label>
  <input
    type="date"
    id="dateAffectation"
    class="form-control"
    formControlName="dateAffectation"
  />
  <div *ngIf="form.get('dateAffectation')?.invalid && (form.get('dateAffectation')?.touched || submitted)" style="color:red">
    La date est requise
  </div>
   <label for="fournisseur" style="font-size: 14px; font-weight: 500; color: #000000;">Fournisseur</label>
<select
  class="form-inputp"
  id="type"
  name="fournisseur"
  formControlName="fournisseurs"
  style="width: 100%;"
  required

>
 <option [ngValue]="null" disabled hidden>Sélectionner fournisseur</option>
  <option *ngFor="let fournisseur of fournisseurs" [ngValue]="fournisseur">
    {{ fournisseur.nomFournisseur }}
  </option>
</select>
<div *ngIf="form.get('fournisseurs')?.invalid && (form.get('fournisseurs')?.touched || submitted)" style="color:red">
  Au moins un fournisseur est requis
</div>


  <!-- 🧩 Image -->
  <label for="image" style="font-size: 14px; font-weight: 500; color: #000000;">Logo d'équipement (optionnel)</label>
  <input 
    type="file" 
    id="image" 
    (change)="onFileSelected($event)" 
    accept="image/*"
    class="form-inputp"
  />
  <div *ngIf="imagePreview" style="margin-top: 10px;">
    <img [src]="imagePreview" alt="Aperçu" style="width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;" />
  </div>

  <br />
  <button type="submit" class="btn btn-primary">
    Enregistrer
  </button>
</form>


  </div>
</div>




















<!-- Modal d'affectation -->
<div class="modal" [ngClass]="{'show': isAffectationModalOpen}" (click)="closeOnOutsideClickAffectation($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeAffectationModal()">&times;</span>
    <h3 style="font-size: 20px; margin-bottom: 20px;">Affecter l'équipement</h3>

    <div *ngIf="selectedEquipement" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
      <h5 style="margin: 0; color: #333;">{{ selectedEquipement.model?.nomModel }}</h5>
      <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">N° Série: {{ selectedEquipement.numSerie }}</p>
    </div>

    <form [formGroup]="affectationForm" (ngSubmit)="onAffectationSubmit()">

<mat-form-field appearance="outline" style="width: 100%;">
  <mat-label>Utilisateur</mat-label>
  <input
    type="text"
    matInput
    [formControl]="utilisateurCtrl"
    [matAutocomplete]="auto"
    placeholder="Rechercher un utilisateur...">
    
  <mat-autocomplete
    #auto="matAutocomplete"
    [displayWith]="displayUtilisateur"
    (optionSelected)="onUserSelected($event.option.value)">
    <mat-option *ngFor="let user of filteredUtilisateurs" [value]="user">
      {{ user.firstName }} {{ user.lastName }} - {{ user.email }}
    </mat-option>
  </mat-autocomplete>
</mat-form-field>


<div *ngIf="!affectationForm.get('user')?.value && affectationFormSubmitted"
     style="color:red; font-size: 12px;">
  L'utilisateur est requis
</div>


      <!-- Commentaire -->
      <label for="commentaire" style="font-size: 14px; font-weight: 500; color: #000000;">Commentaire (optionnel)</label>
      <textarea
        formControlName="commentaire"
        class="form-inputp"
        rows="3"
        placeholder="Commentaire sur l'affectation (optionnel)..."
        style="width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;">
      </textarea>

      <!-- Date d'affectation -->
      <label for="dateAffectation" style="font-size: 14px; font-weight: 500; color: #000000;">Date d'affectation (optionnel)</label>
      <input
        type="date"
        formControlName="dateAffectation"
        class="form-inputp"
        style="width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;">

      <br />
      <button type="submit" class="btn-submit">
         Affecter l'équipement
      </button>
    </form>
  </div>
</div>






<!-- Modal d'affectation -->
<div class="modal" [ngClass]="{'show': isAffectationEditModalOpen}" (click)="closeOnOutsideClickAffectationEdit($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeAffectationEditModal()">&times;</span>
    <h3 style="font-size: 20px; margin-bottom: 20px;">Affecter l'équipement</h3>

    <div *ngIf="selectedEquipement" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
      <h5 style="margin: 0; color: #333;">{{ selectedEquipement.model?.nomModel }}</h5>
      <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">N° Série: {{ selectedEquipement.numSerie }}</p>
    </div>

<form (ngSubmit)="selectedEquipement && updateReaffication(selectedEquipement)" #editAffectationForm="ngForm">

  <!-- Utilisateur -->
  <mat-form-field appearance="outline" style="width: 100%;">
    <mat-label>Utilisateur Actuel</mat-label>
 <input
  type="text"
  matInput
  [formControl]="utilisateurCtrl"
  [matAutocomplete]="auto"
  placeholder="Rechercher un utilisateur..."
  required>

    <mat-autocomplete 
      #auto="matAutocomplete" 
      [displayWith]="displayUtilisateur"
      (optionSelected)="onUserSelected($event.option.value)">
      <mat-option *ngFor="let user of filteredUtilisateurs" [value]="user">
        {{ user.firstName }} {{ user.lastName }} - {{ user.email }}
      </mat-option>
    </mat-autocomplete>
  </mat-form-field>

  <div *ngIf="!EditedAffectation.user && editAffectationFormSubmitted" style="color:red; font-size: 12px;">
    L'utilisateur est requis
  </div>

  <!-- Commentaire -->
  <label for="commentaire" style="font-size: 14px; font-weight: 500; color: #000000;">Commentaire (optionnel)</label>
  <textarea
    name="commentaire"
    [(ngModel)]="EditedAffectation.commentaire"
    class="form-inputp"
    rows="3"
    placeholder="Commentaire sur l'affectation (optionnel)..."
    style="width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;">
  </textarea>

  <!-- Date d'affectation -->
  <label for="dateAffectation" style="font-size: 14px; font-weight: 500; color: #000000;">Date d'affectation (optionnel)</label>
  <input
    type="date"
    name="dateAffectation"
    [(ngModel)]="EditedAffectation.dateAffectation"
    class="form-inputp"
    style="width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;">

  <!-- Submit -->
  <br />
  <button type="submit" class="btn-submit">
  Modifier Affectation
  </button>
</form>

  </div>
</div>







<style>
    .card-custom {
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .btn-outline-lightblue {
      border: 1px solid #cfe2ff;
      color: #0d6efd;
      background-color: #e7f1ff;
    }

    .tag {
      background-color: #e7f1ff;
      color: #0d6efd;
      padding: 3px 10px;
      font-size: 0.8rem;
      border-radius: 15px;
      position: absolute;
      right: 20px;
      top: 20px;
    }

.icon-box {
  font-size: 48px; /* optional - for icon size */
  width: 100px;     /* increase width */
  height: 100px;    /* set height */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0d6efd;
  margin-right: 10px;
 border-radius: 0% !important;
}

    .btn-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

.card-custom {
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  background-color: #fff;
  color: #212529; /* Darker text */
  font-size: 0.95rem; /* Slightly larger base font */
}

.card-custom strong {
  font-weight: 600; /* Heavier for labels */
  color: #1a1a1a;
}

.card-custom h5 {
  font-weight: 600;
  color: #000;
}

.card-custom small,
.text-muted {
  color: #495057 !important; /* Less faded gray */
}

.icon-box {
  font-size: 32px;
  color: #0d6efd;
  margin-right: 10px;
}

.tag {
  background-color: #e7f1ff;
  color: #0d6efd;
  padding: 3px 10px;
  font-size: 0.8rem;
  border-radius: 15px;
  position: absolute;
  right: 20px;
  top: 20px;
}



  </style>

<body class="bg-light">
  <div class="container my-5">

    <!-- Simple Notification Bar -->
    <div *ngIf="notification.show" class="simple-notification" [ngClass]="notification.type">
      {{ notification.message }}
    </div>

    <!-- Tableau des équipements -->
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="table-responsive mt-4">
                <table class="table mb-0 text-nowrap varient-table align-middle fs-3">
                  <thead>
                    <tr>
                      <th scope="col" class="px-0 text-muted">Image</th>
                      <th scope="col" class="px-0 text-muted">Modèle</th>
                      <th scope="col" class="px-0 text-muted">N° Série</th>
                      <th scope="col" class="px-0 text-muted">Date d'acquisition</th>
                      <th scope="col" class="px-0 text-muted">Statut</th>
                      <th scope="col" class="px-0 text-muted">Description</th>
                      <th scope="col" class="px-0 text-muted">Fournisseur</th>
                      <th scope="col" class="px-0 text-muted text-end">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let equip of equiements">
                      <!-- Image -->
                      <td class="px-1">
                        <img [src]="equip.image"
                             alt="Équipement"
                             class="rounded-circle img-fluid"
                             width="40" height="40" />
                      </td>

                      <!-- Modèle -->
                      <td class="px-1">
                        <div class="ms-3">
                          <h6 class="fw-semibold mb-0 fs-4">{{ equip.model?.nomModel }}</h6>
                          <span class="fw-normal text-muted">Modèle</span>
                        </div>
                      </td>

                      <!-- Numéro de série -->
                      <td class="px-1">
                        <span class="fw-normal">{{ equip.numSerie }}</span>
                      </td>

                      <!-- Date d'acquisition -->
                      <td class="px-1">
                        <span class="fw-normal">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>
                      </td>

                      <!-- Statut -->
                      <td class="px-1">
                        <span class="badge rounded-pill"
                              [style.background-color]="equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')"
                              [style.color]="'white'">
                          {{ equip.statut }}
                        </span>
                        <div *ngIf="equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'"
                             class="text-muted small mt-1">
                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}
                        </div>
                      </td>

                      <!-- Description -->
                      <td class="px-1">
                        <span class="fw-normal text-truncate" style="max-width: 150px; display: inline-block;"
                              [title]="equip.description">
                          {{ equip.description }}
                        </span>
                      </td>

                      <!-- Fournisseur -->
                      <td class="px-1">
                        <span class="fw-normal">{{ equip.fournisseur?.nomFournisseur || 'Aucun fournisseur' }}</span>
                      </td>

                      <!-- Actions -->
                      <td class="px-1 text-end">
                        <div class="d-flex justify-content-end gap-1">
                          <!-- Bouton d'affectation conditionnel -->
                          <button
                            *ngIf="equip.statut === 'DISPONIBLE'"
                            class="btn btn-sm btn-outline-primary"
                            (click)="openAffectationModal(equip)"
                            title="Affecter">
                            Affecter
                          </button>
                          <button
                            *ngIf="equip.statut === 'AFFECTE'"
                            class="btn btn-sm btn-outline-warning"
                            (click)="openEditedModal(equip)"
                            title="Réaffecter">
                            🔄
                          </button>
                          <button
                            *ngIf="equip.statut === 'AFFECTE'"
                            class="btn btn-sm btn-outline-secondary"
                            (click)="desaffecterEquipement(equip)"
                            title="Désaffecter">
                            Désaffecter
                          </button>
                          <button
                            class="btn btn-sm btn-outline-primary"
                            (click)="openModal1(equip)"
                            title="Modifier">
                            ✏️
                          </button>
                          <button
                            class="btn btn-sm btn-outline-danger"
                            (click)="confirmDelete(equip.idEqui)"
                            title="Supprimer">
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- Pagination Bootstrap -->
<nav class="mt-4 d-flex justify-content-center" *ngIf="totalPages > 1">
  <ul class="pagination">
    <li class="page-item" [class.disabled]="currentPage === 0">
      <a class="page-link" (click)="loadEquipements(currentPage - 1)">Précédent</a>
    </li>

    <li class="page-item"
        *ngFor="let page of [].constructor(totalPages); let i = index"
        [class.active]="i === currentPage">
      <a class="page-link" (click)="loadEquipements(i)">{{ i + 1 }}</a>
    </li>

    <li class="page-item" [class.disabled]="currentPage === totalPages - 1">
      <a class="page-link" (click)="loadEquipements(currentPage + 1)">Suivant</a>
    </li>
  </ul>
</nav>

  </div>
</body>



          <!--  Row 1 -->
          <div class="row">
            
          <div class="py-6 px-6 text-center">
            <p class="mb-0 fs-4">Design and Developed by <a href="#"
                class="pe-1 text-primary text-decoration-underline">Wrappixel.com</a> Distributed by <a href="https://themewagon.com" target="_blank" >ThemeWagon</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="./assets/libs/jquery/dist/jquery.min.js"></script>
  <script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="./assets/js/sidebarmenu.js"></script>
  <script src="./assets/js/app.min.js"></script>
  <script src="./assets/libs/apexcharts/dist/apexcharts.min.js"></script>
  <script src="./assets/libs/simplebar/dist/simplebar.js"></script>
  <script src="./assets/js/dashboard.js"></script>
  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
</body>

</html>