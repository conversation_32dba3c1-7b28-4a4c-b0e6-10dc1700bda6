<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>
  
</head>

<body>
  <!--  Body Wrapper -->
  <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
    data-sidebar-position="fixed" data-header-position="fixed">

    <!--  App Topstrip -->
    
    <!-- Sidebar Start -->
 <app-navebar></app-navebar>


    <!--  Sidebar End -->
    <!--  Main wrapper -->
    <div class="body-wrapper">
      <!--  Header Start -->
      <header class="app-header">

      </header>
      <!--  Header End -->
      <div class="body-wrapper-inner">
        <div class="container-fluid">
                <div class="welcome-header">
  <h1>Bienvenue dans votre espace</h1>
  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>

</div>
<!-- Bouton pour ouvrir la modale -->

<div class="header-container">
  <div class="header-text">
    <h2>Fournisseur</h2>
    <p>Gérez les différents fournisseurs

</p>
  </div>
<button class="add-user-btn" style="width: 200px;" (click)="openModal()">
  <span class="icon">+</span>Nouveau fournisseur

</button>
</div>
<div class="search-wrapper">
  <div class="custom-search">
    <input type="text" placeholder="Rechercher un type..." />
    <span class="icon-search"></span>
  </div>
</div>
<!-- Modal -->




<div class="modal fade" id="updateModal" tabindex="-1" aria-labelledby="updateModalLabel" aria-hidden="true" data-bs-backdrop="false">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content shadow rounded-4">
      
      <h5 id="updateModalLabel">📝 Modifier les informations</h5>
      <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>

      <div class="modal-body">
        <form #updateForm="ngForm">
          <!-- Nom du type -->
          <div class="mb-4">
            <label for="nomFournisseur" class="form-label fw-semibold fs-5">Nom du type</label>
            <input
              type="text"
              class="form-control"
              id="nomFournisseur"
              name="nomFournisseur"
              [(ngModel)]="fournisseur1.nomFournisseur"
              #nomMarque="ngModel"
              required
              minlength="3"
            />
            <div *ngIf="nomMarque.invalid && nomMarque.touched" style="color:red">
              <div *ngIf="nomMarque.errors?.['required']">Le nom de Marque est requis</div>
              <div *ngIf="nomMarque.errors?.['minlength']">Le nom de marque doit contenir au moins 2 caractères</div>
            </div>
          </div>

          <!-- Types disponibles -->
          
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Annuler
        </button>
        <button type="button" class="btn btn-success px-4" (click)="onUpdateClick(updateForm)">
          💾 Sauvegarder
        </button>
      </div>
    </div>
  </div>
</div>




<body class="bg-light">


<div class="modal" [ngClass]="{'show': isModalOpen}" (click)="closeOnOutsideClick($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeModal()">&times;</span>
    <h3 style="font-size: 20px; margin-bottom: -10px;" >Ajouter un nouveau fournisseur</h3>
<form [formGroup]="fournisseurForm" (ngSubmit)="onRegister()" novalidate>
  <br><br>

  <!-- 🧩 Model -->





  <label for="numSerie" style="font-size: 14px; font-weight: 500; color: #000000;">Nom Fournisseur</label>
  <input
    class="form-inputp"
    id="nomFournisseur"
    type="text"
    formControlName="nomFournisseur"
    placeholder="Nom du Fournisseur"
  />
  <div *ngIf="fournisseurForm.get('nomFournisseur')?.invalid && (fournisseurForm.get('nomFournisseur')?.touched || submitted)" style="color:red">
     Nom de Fournisseur est requis (min 3 caractères)
  </div>
     <label for="emailFournisseur" style="font-size: 14px; font-weight: 500; color: #000000;">Email</label>
  <input
    class="form-inputp"
    id="emailFournisseur"
    type="text"
    formControlName="emailFournisseur"
    placeholder="<EMAIL>"
  />
  <div *ngIf="fournisseurForm.get('emailFournisseur')?.invalid && (fournisseurForm.get('emailFournisseur')?.touched || submitted)" style="color:red">
Email est requis 
  </div>



     <label for="telephoneFournisseur" style="font-size: 14px; font-weight: 500; color: #000000;">Telephone</label>
  <input
    class="form-inputp"
    id="telephoneFournisseur"
    type="text"
    formControlName="telephoneFournisseur"
    placeholder="012345678"
  />
  <div *ngIf="fournisseurForm.get('telephoneFournisseur')?.invalid && (fournisseurForm.get('telephoneFournisseur')?.touched || submitted)" style="color:red">
    Le numéro de téléphone est requis (min 4 caractères)
  </div>
  
  <label for="adresseFournisseur" style="font-size: 14px; font-weight: 500; color: #000000;">Adresse</label>
  <textarea
    class="form-inputp"
    id="adresseFournisseur"
    type="text"
    formControlName="adresseFournisseur"
    placeholder="Adresse"
  ></textarea>
  <div *ngIf="fournisseurForm.get('adresseFournisseur')?.invalid && (fournisseurForm.get('adresseFournisseur')?.touched || submitted)" style="color:red">
    Adesse est requis (min 4 caractères)
  </div>

  

  <br />
  <button type="submit"  class="btn btn-primary">
    Enregistrer
  </button>
</form>


  </div>
</div>







     <!-- Simple Notification Bar -->
     <div *ngIf="notification.show" class="simple-notification" [ngClass]="notification.type">
       {{ notification.message }}
     </div>

     <div class="container">
      <div  *ngFor="let fournisseur of fournisseurs" class="card">
      <div class="header">
       <div class="icon">
  <img src="https://img.icons8.com/ios-filled/50/4caf50/company.png" alt="icon" style="width: 60px; height: 60px;" />
</div>

        <div class="info">
          <h2>{{fournisseur.nomFournisseur}}</h2>
          <p>Depuis 12/01/2024</p>
        </div>

      </div>
      <div class="details">
     <p>
  <img src="assets/images/logos/human.JPG" alt="Marie Martin" style="width: 23px; height: 30px; border-radius: 50%; vertical-align: middle; margin-right: 8px;">
  {{fournisseur.nomFournisseur}}
</p>
   <p>
  <img src="assets/images/logos/mail.JPG" alt="Marie Martin" style="width: 28x; height: 28px; border-radius: 50%; vertical-align: middle; margin-right: 8px;">
{{fournisseur.emailFournisseur}}
</p>
   <p> <img src="assets/images/logos/phone.JPG" alt="Marie Martin" style="width: 28x; height: 28px; border-radius: 50%; vertical-align: middle; margin-right: 8px;">
{{fournisseur.telephoneFournisseur}}

   <p> <img src="assets/images/logos/map.JPG" alt="Marie Martin" style="width: 28x; height: 28px; border-radius: 50%; vertical-align: middle; margin-right: 8px;">
{{fournisseur.adresseFournisseur}}


      </div>
      <div class="actions">
        <button class="view" (click)="deleteFournisseur(fournisseur.idFournisseur)">
  <img src="assets/images/logos/delete.JPG" alt="Supprimer" style="width:15px; height: 15px; margin-right: 8px; vertical-align: middle;">
  Supprimer
</button>
     <button class="edit" >
  <img src="assets/images/logos/edittt.JPG" alt="Modifier" style="width:40px; height: 25px; margin-right: 8px; vertical-align: middle;">
  Modifier
</button>

      </div>
    </div>



 
  </div>







</body>









<!-- MODAL -->




          <!--  Row 1 -->
          <div class="row">
           




       
          </div>
          <div class="py-6 px-6 text-center">
            <p class="mb-0 fs-4">Design and Developed by <a href="#"
                class="pe-1 text-primary text-decoration-underline">Wrappixel.com</a> Distributed by <a href="https://themewagon.com" target="_blank" >ThemeWagon</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="./assets/libs/jquery/dist/jquery.min.js"></script>
  <script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="./assets/js/sidebarmenu.js"></script>
  <script src="./assets/js/app.min.js"></script>
  <script src="./assets/libs/apexcharts/dist/apexcharts.min.js"></script>
  <script src="./assets/libs/simplebar/dist/simplebar.js"></script>
  <script src="./assets/js/dashboard.js"></script>
  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
</body>

</html><p>fournisseur works!</p>
